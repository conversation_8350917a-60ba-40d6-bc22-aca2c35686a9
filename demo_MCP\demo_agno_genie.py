import subprocess
from pathlib import Path
#from agno.tools.base import BaseTool
#from agno.agent import Agent
from typing import Optional
from agno.models.openai import OpenAIChat
import asyncio

from agno.agent import Agent, RunResponse
from agno.models.openrouter import OpenRouter

class GeNIeTool():
    """用于与GeNIe软件交互的工具"""
    
    def __init__(self):
        super().__init__()
        self.genie_path = r"C:\Program Files\GeNIe 5.0 (64-bit)\genie.exe"
        self.default_model = None
    
    async def execute(self, model_path: Optional[str] = None) -> str:
        """启动GeNIe并打开指定的模型文件
        
        Args:
            model_path: 要打开的模型文件路径，如果为None则打开默认模型
            
        Returns:
            操作结果描述
        """
        if model_path is None:
            if self.default_model is None:
                return "错误：未提供模型路径且未设置默认模型"
            model_path = self.default_model
        
        # 检查文件是否存在
        if not Path(model_path).exists():
            return f"错误：模型文件不存在 - {model_path}"
        
        try:
            # 使用subprocess启动GeNIe并打开模型
            subprocess.Popen([self.genie_path, model_path])
            return f"成功：已启动GeNIe并打开模型 {model_path}"
        except Exception as e:
            return f"错误：无法启动GeNIe - {str(e)}"
    
    def set_default_model(self, model_path: str) -> str:
        """设置默认模型路径"""
        if Path(model_path).exists():
            self.default_model = model_path
            return f"成功：默认模型已设置为 {model_path}"
        return f"错误：模型文件不存在 - {model_path}"
    
def create_genie_agent():
    # 初始化Agno智能体
    # agent = Agent(
    #     model=OpenAIChat(model="gpt-4"),  # 使用OpenAI模型
    #     name="GeNIe助手",
    #     description="能够启动GeNIe软件并打开指定模型的智能助手"

    agent = Agent(
        model=OpenRouter(id="gpt-4o"),
        markdown=True
        )
    

    
    
    # 添加GeNIe工具
    genie_tool = GeNIeTool()
    #agent.add_tool(genie_tool, "genie_tool")
    agent.add_tool(genie_tool)
    
    # 设置默认模型（可选）
    genie_tool.set_default_model(r"C:\MAU.xdsl")
    
    return agent

async def main():
    # 创建智能体实例
    genie_agent = create_genie_agent()

    # 示例1：打开默认模型
    response = await genie_agent.run("请打开GeNIe中的默认模型")
    print(response)

    # 示例2：打开特定模型
    response = await genie_agent.run("请打开D:\\models\\project1.xdsl这个模型")
    print(response)


if __name__ == "__main__":
  asyncio.run(main())